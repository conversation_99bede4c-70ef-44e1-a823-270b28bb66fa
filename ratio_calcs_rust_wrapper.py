import numpy as np
import pandas as pd

# Try to import the Rust module, fall back to original if not available
try:
    import ratio_calcs_rust
    RUST_AVAILABLE = True
except ImportError:
    RUST_AVAILABLE = False
    # Import original functions as fallback
    from ratio_calcs import (
        portfolio_variance_numba, neg_sharpe_ratio_numba, downside_std_numba,
        neg_sortino_ratio_numba, calculate_var_cvar_numba,
        neg_modified_sharpe_ratio_numba
    )

MIN_FLOAT = 1e-15

def portfolio_variance_numba(weights, cov_matrix):
    """Portfolio variance calculation using Rust if available"""
    if RUST_AVAILABLE:
        return ratio_calcs_rust.portfolio_variance_rust(
            np.array(weights, dtype=np.float64),
            np.array(cov_matrix, dtype=np.float64)
        )
    else:
        # Fallback to original implementation
        return np.dot(weights, np.dot(cov_matrix, weights))

def portfolio_variance(weights, cov_matrix):
    return portfolio_variance_numba(np.array(weights), np.array(cov_matrix))

def neg_sharpe_ratio_numba(weights, mean_returns, cov_matrix):
    """Negative Sharpe ratio calculation using Rust if available"""
    if RUST_AVAILABLE:
        return ratio_calcs_rust.neg_sharpe_ratio_rust(
            np.array(weights, dtype=np.float64),
            np.array(mean_returns, dtype=np.float64),
            np.array(cov_matrix, dtype=np.float64)
        )
    else:
        # Fallback implementation
        port_return = 0.0
        n = len(weights)
        for i in range(n):
            w = weights[i]
            mr = mean_returns[i]
            if w != w:  # if NaN
                w = 0.0
            if mr != mr:
                mr = 0.0
            port_return += w * mr

        port_var = portfolio_variance_numba(weights, cov_matrix)
        if port_var != port_var:
            port_var = 0.0
        port_vol = np.sqrt(port_var)
        if port_vol != port_vol:
            port_vol = 0.0

        if port_vol > 0.0:
            return -port_return / port_vol
        else:
            return 0.0

def neg_sharpe_ratio(weights, mean_returns, cov_matrix):
    return neg_sharpe_ratio_numba(np.array(weights), np.array(mean_returns), np.array(cov_matrix))

def downside_std_numba(arr):
    """Downside standard deviation using Rust if available"""
    if RUST_AVAILABLE:
        return ratio_calcs_rust.downside_std_rust(np.array(arr, dtype=np.float64))
    else:
        # Fallback implementation
        s = 0.0
        count = 0
        for x in arr:
            if x != x:  # Check if not NaN
                continue
            if x < 0:
                s += x * x
                count += 1
        if count > 0:
            return np.sqrt(s / count)
        else:
            return 0.0

def neg_sortino_ratio_numba(weights, mean_returns, cov_matrix, returns_array):
    """Negative Sortino ratio using Rust if available"""
    if RUST_AVAILABLE:
        return ratio_calcs_rust.neg_sortino_ratio_rust(
            np.array(weights, dtype=np.float64),
            np.array(mean_returns, dtype=np.float64),
            np.array(cov_matrix, dtype=np.float64),
            np.array(returns_array, dtype=np.float64)
        )
    else:
        # Fallback implementation
        port_return = 0.0
        n = len(weights)
        for i in range(n):
            port_return += weights[i] * mean_returns[i]
        
        clean_returns = np.empty_like(returns_array)
        for i in range(returns_array.shape[0]):
            val = returns_array[i]
            if val != val:
                clean_returns[i] = 0.0
            else:
                clean_returns[i] = val

        dd = downside_std_numba(clean_returns)
        if dd < MIN_FLOAT:
            return np.inf
        return -port_return / dd

def neg_sortino_ratio(weights, mean_returns, cov_matrix, adjusted):
    candidate_series = adjusted.dot(weights).values.astype(np.float64)
    return neg_sortino_ratio_numba(np.array(weights),
                                   np.array(mean_returns),
                                   np.array(cov_matrix),
                                   candidate_series)





def calculate_var_cvar_numba(returns_array, confidence_level=0.95):
    """VaR and CVaR calculation using Rust if available"""
    if RUST_AVAILABLE:
        return ratio_calcs_rust.calculate_var_cvar_rust(
            np.array(returns_array, dtype=np.float64),
            float(confidence_level)
        )
    else:
        # Fallback to original implementation
        from ratio_calcs import calculate_var_cvar_numba as original_var_cvar
        return original_var_cvar(returns_array, confidence_level)

def calculate_var_cvar(returns_series, confidence_level=0.95):
    """Calculate VaR and CVaR for a return series"""
    if len(returns_series) == 0:
        return 0.0, 0.0

    clean_returns = np.array([x for x in returns_series if np.isfinite(x)])

    if len(clean_returns) == 0:
        return 0.0, 0.0

    return calculate_var_cvar_numba(clean_returns, confidence_level)



def neg_modified_sharpe_ratio_numba(weights, mean_returns, cov_matrix, returns_array, risk_free_rate=0.0):
    """Modified Sharpe ratio calculation using Rust if available"""
    if RUST_AVAILABLE:
        return ratio_calcs_rust.neg_modified_sharpe_ratio_rust(
            np.array(weights, dtype=np.float64),
            np.array(mean_returns, dtype=np.float64),
            np.array(cov_matrix, dtype=np.float64),
            np.array(returns_array, dtype=np.float64),
            float(risk_free_rate)
        )
    else:
        # Fallback to original implementation
        from ratio_calcs import neg_modified_sharpe_ratio_numba as original_modified_sharpe
        return original_modified_sharpe(weights, mean_returns, cov_matrix, returns_array, risk_free_rate)

def neg_modified_sharpe_ratio(weights, mean_returns, cov_matrix, adjusted, risk_free_rate=0.0):
    """
    Wrapper for the Numba-optimized modified Sharpe ratio calculation.
    'adjusted' is expected to be a pandas DataFrame or similar; its values will be used to compute the portfolio return series.
    """
    if hasattr(adjusted, 'values'):
        arr = adjusted.values.astype(np.float64)
    else:
        arr = np.array(adjusted, dtype=np.float64)
    return neg_modified_sharpe_ratio_numba(
        np.array(weights, dtype=np.float64),
        np.array(mean_returns, dtype=np.float64),
        np.array(cov_matrix, dtype=np.float64),
        arr,
        risk_free_rate
    )

def neg_var_ratio(weights, mean_returns, cov_matrix, adjusted, confidence_level=0.95):
    """Calculate negative VaR ratio (return/VaR) for minimization"""
    try:
        port_return = np.dot(weights, mean_returns)

        # Generate return series
        candidate_series = adjusted.dot(weights)
        if hasattr(candidate_series, 'values'):
            values_array = candidate_series.values.astype(np.float64)
        else:
            values_array = np.array(candidate_series, dtype=np.float64)

        clean_values = values_array[~np.isnan(values_array)]

        if len(clean_values) > 0:
            var, _ = calculate_var_cvar_numba(clean_values, confidence_level)
        else:
            var = MIN_FLOAT

        if abs(var) < MIN_FLOAT:
            return 0.0

        return -port_return / var

    except (ValueError, TypeError) as e:
        print(f"Error in VaR calculation: {str(e)}")
        return 0.0

def neg_cvar_ratio(weights, mean_returns, cov_matrix, adjusted, confidence_level=0.95):
    """Calculate negative CVAR ratio (return/CVaR) for minimization"""
    try:
        port_return = np.dot(weights, mean_returns)

        # Generate return series
        candidate_series = adjusted.dot(weights)
        if hasattr(candidate_series, 'values'):
            values_array = candidate_series.values.astype(np.float64)
        else:
            values_array = np.array(candidate_series, dtype=np.float64)

        clean_values = values_array[~np.isnan(values_array)]

        if len(clean_values) > 0:
            _, cvar = calculate_var_cvar_numba(clean_values, confidence_level)
        else:
            cvar = MIN_FLOAT

        if abs(cvar) < MIN_FLOAT:
            return 0.0

        return -port_return / cvar

    except (ValueError, TypeError) as e:
        print(f"Error in CVaR calculation: {str(e)}")
        return 0.0

def neg_total_return(weights, mean_returns):
    port_return = np.dot(weights, mean_returns)
    return -port_return

def portfolio_volatility(weights, cov_matrix):
    port_var = np.dot(weights.T, np.dot(cov_matrix, weights))
    port_vol = np.sqrt(port_var)
    return port_vol
