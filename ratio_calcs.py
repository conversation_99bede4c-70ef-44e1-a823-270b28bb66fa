import numpy as np
from numba import njit
import pandas as pd

MIN_FLOAT = 1e-15

@njit
def portfolio_variance_numba(weights, cov_matrix):
    return np.dot(weights, np.dot(cov_matrix, weights))

def portfolio_variance(weights, cov_matrix):
    return portfolio_variance_numba(np.array(weights), np.array(cov_matrix))

@njit
def neg_sharpe_ratio_numba(weights, mean_returns, cov_matrix):
    port_return = 0.0
    n = len(weights)
    # Accumulate weighted returns with safeguard.
    for i in range(n):
        w = weights[i]
        mr = mean_returns[i]
        if w != w:  # if NaN
            w = 0.0
        if mr != mr:
            mr = 0.0
        port_return += w * mr

    # Compute variance using our numba function.
    port_var = portfolio_variance_numba(weights, cov_matrix)
    if port_var != port_var:
        port_var = 0.0
    port_vol = np.sqrt(port_var)
    if port_vol != port_vol:
        port_vol = 0.0

    if port_vol > 0.0:
        return -port_return / port_vol
    else:
        return 0.0

def neg_sharpe_ratio(weights, mean_returns, cov_matrix):
    return neg_sharpe_ratio_numba(np.array(weights), np.array(mean_returns), np.array(cov_matrix))

@njit
def downside_std_numba(arr):
    s = 0.0
    count = 0
    for x in arr:
        # If x is NaN, skip it.
        if x != x:
            continue
        if x < 0:
            s += x * x
            count += 1
    if count > 0:
        return np.sqrt(s / count)
    else:
        return 0.0

@njit
def neg_sortino_ratio_numba(weights, mean_returns, cov_matrix, returns_array):
    port_return = 0.0
    n = len(weights)
    for i in range(n):
        port_return += weights[i] * mean_returns[i]
    
    # Clean the returns_array: replace NaNs with 0.
    clean_returns = np.empty_like(returns_array)
    for i in range(returns_array.shape[0]):
        val = returns_array[i]
        if val != val:
            clean_returns[i] = 0.0
        else:
            clean_returns[i] = val

    # Compute downside standard deviation on negative returns.
    dd = downside_std_numba(clean_returns)
    if dd < MIN_FLOAT:
        return np.inf
    return -port_return / dd

def neg_sortino_ratio(weights, mean_returns, cov_matrix, adjusted):
    # Convert candidate_series to numpy: adjusted.dot(weights)
    candidate_series = adjusted.dot(weights).values.astype(np.float64)
    return neg_sortino_ratio_numba(np.array(weights),
                                   np.array(mean_returns),
                                   np.array(cov_matrix),
                                   candidate_series)











def neg_total_return(weights, mean_returns):
    port_return = np.dot(weights, mean_returns)
    return -port_return

def portfolio_volatility(weights, cov_matrix):
    port_var = np.dot(weights.T, np.dot(cov_matrix, weights))
    port_vol = np.sqrt(port_var)
    return port_vol

def calculate_var_cvar(returns_series, confidence_level=0.95):
    """Calculate VaR and CVaR for a return series"""
    # Handle empty arrays
    if len(returns_series) == 0:
        return 0.0, 0.0
        
    # Clean the input array (remove NaNs)
    clean_returns = np.array([x for x in returns_series if np.isfinite(x)])
    
    # If no valid returns after cleaning, return zeros
    if len(clean_returns) == 0:
        return 0.0, 0.0
    
    # Sort returns from worst to best
    sorted_returns = np.sort(clean_returns)
    
    # Find the index at the specified confidence level
    index = int((1 - confidence_level) * len(sorted_returns))
    if index < 0:
        index = 0
    
    # VaR is the loss at this confidence level (negative of return)
    var = -sorted_returns[index]
    
    # CVaR is the average loss beyond VaR (negative of average returns below threshold)
    tail_returns = sorted_returns[:index+1]  # Include the VaR point
    if len(tail_returns) > 0:
        cvar = -np.mean(tail_returns)
    else:
        cvar = var  # Fallback if we don't have enough data points
    
    return var, cvar

@njit
def calculate_var_cvar_numba(returns_array, confidence_level=0.95):
    """Numba-optimized VaR and CVaR calculation"""
    # Clean the input array (remove NaNs)
    valid_returns = []
    for x in returns_array:
        if x == x:  # Check if not NaN
            valid_returns.append(x)
    
    # If no valid returns, return zeros
    if len(valid_returns) == 0:
        return 0.0, 0.0
    
    # Sort returns from worst to best
    sorted_returns = np.sort(np.array(valid_returns))
    
    # Find the index at the specified confidence level
    index = int((1 - confidence_level) * len(sorted_returns))
    if index < 0:
        index = 0
    
    # VaR is the loss at this confidence level (negative of return)
    var = -sorted_returns[index]
    
    # CVaR is the average loss beyond VaR (including the VaR point)
    tail_index = index + 1 # Include the VaR point
    if tail_index > 0 and tail_index <= len(sorted_returns):
        tail_sum = 0.0
        for i in range(tail_index): # Loop up to and including index
            tail_sum += sorted_returns[i]
        cvar = -tail_sum / tail_index # Divide by the number of points included
    else:
        cvar = var # Fallback if index is 0 or out of bounds
    
    return var, cvar

def neg_var_ratio(weights, mean_returns, cov_matrix, adjusted, confidence_level=0.95):
    """Calculate negative VaR ratio (return/VaR) for minimization"""
    try:
        port_return = np.dot(weights, mean_returns)

        # Generate return series
        candidate_series = adjusted.dot(weights)
        if hasattr(candidate_series, 'values'):
            values_array = candidate_series.values.astype(np.float64)
        else:
            values_array = np.array(candidate_series, dtype=np.float64)

        clean_values = values_array[~np.isnan(values_array)]

        if len(clean_values) > 0:
            var, _ = calculate_var_cvar_numba(clean_values, confidence_level)
        else:
            var = MIN_FLOAT

        if abs(var) < MIN_FLOAT:
            return 0.0

        return -port_return / var

    except (ValueError, TypeError) as e:
        print(f"Error in VaR calculation: {str(e)}")
        return 0.0

def neg_cvar_ratio(weights, mean_returns, cov_matrix, adjusted, confidence_level=0.95):
    """Calculate negative CVAR ratio (return/CVaR) for minimization"""
    try:
        port_return = np.dot(weights, mean_returns)

        # Generate return series
        candidate_series = adjusted.dot(weights)
        if hasattr(candidate_series, 'values'):
            values_array = candidate_series.values.astype(np.float64)
        else:
            values_array = np.array(candidate_series, dtype=np.float64)

        clean_values = values_array[~np.isnan(values_array)]

        if len(clean_values) > 0:
            _, cvar = calculate_var_cvar_numba(clean_values, confidence_level)
        else:
            cvar = MIN_FLOAT

        if abs(cvar) < MIN_FLOAT:
            return 0.0

        return -port_return / cvar

    except (ValueError, TypeError) as e:
        print(f"Error in CVaR calculation: {str(e)}")
        return 0.0







@njit
def neg_modified_sharpe_ratio_numba(weights, mean_returns, cov_matrix, returns_array, risk_free_rate=0.0):
    n = len(weights)
    # Compute portfolio expected return using mean returns
    port_return = 0.0
    for i in range(n):
        port_return += weights[i] * mean_returns[i]
    
    # Compute portfolio variance
    port_var = 0.0
    for i in range(n):
        for j in range(n):
            port_var += weights[i] * cov_matrix[i, j] * weights[j]
    port_vol = np.sqrt(port_var)
    if port_vol <= MIN_FLOAT:
        return 0.0

    # Compute portfolio return series from returns_array (each row is a return observation)
    T = returns_array.shape[0]
    port_series = np.empty(T, dtype=np.float64)
    for t in range(T):
        s = 0.0
        for j in range(n):
            s += returns_array[t, j] * weights[j]
        port_series[t] = s

    # Calculate sample mean and standard deviation of the series
    T_float = float(T)
    series_mean = 0.0
    for t in range(T):
        series_mean += port_series[t]
    series_mean /= T_float

    series_std = 0.0
    for t in range(T):
        diff = port_series[t] - series_mean
        series_std += diff * diff
    series_std = np.sqrt(series_std / T_float)
    
    # Compute skewness and kurtosis
    skew = 0.0
    kurt = 0.0
    if series_std > MIN_FLOAT:
        for t in range(T):
            diff_norm = (port_series[t] - series_mean) / series_std
            skew += diff_norm ** 3
            kurt += diff_norm ** 4
        skew /= T_float
        kurt /= T_float
    else:
        skew = 0.0
        kurt = 3.0  # Normal distribution assumption

    # Use the 95% confidence level: standard normal quantile.
    z = 1.****************
    # Cornish–Fisher expansion adjustment
    z_mod = z + (1.0/6.0) * (z*z - 1.0) * skew + (1.0/24.0) * (z**3 - 3.0*z) * (kurt - 3.0) - (1.0/36.0) * (2.0*z**3 - 5.0*z) * (skew**2)
    # Adjust volatility: scaling the normal volatility to account for skew/kurtosis
    mod_vol = port_vol * (z_mod / z)
    
    # Modified Sharpe ratio (for minimization, return negative value)
    sharpe_mod = (port_return - risk_free_rate) / mod_vol
    return -sharpe_mod

def neg_modified_sharpe_ratio(weights, mean_returns, cov_matrix, adjusted, risk_free_rate=0.0):
    """
    Wrapper for the Numba-optimized modified Sharpe ratio calculation.
    'adjusted' is expected to be a pandas DataFrame or similar; its values will be used to compute the portfolio return series.
    """
    # Convert to numpy array if needed.
    if hasattr(adjusted, 'values'):
        arr = adjusted.values.astype(np.float64)
    else:
        arr = np.array(adjusted, dtype=np.float64)
    return neg_modified_sharpe_ratio_numba(
        np.array(weights, dtype=np.float64),
        np.array(mean_returns, dtype=np.float64),
        np.array(cov_matrix, dtype=np.float64),
        arr,
        risk_free_rate
    )
