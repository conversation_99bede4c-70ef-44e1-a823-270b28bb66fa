use pyo3::prelude::*;
use numpy::{PyReadonlyArray1, PyR<PERSON>onlyArray2, PyArray1};
use ndarray::{Array1, Array2};
use std::collections::VecDeque;
use argmin::core::{CostFunction, Gradient};
use rayon::prelude::*;
use serde::{Deserialize, Serialize};

const MIN_FLOAT: f64 = 1e-15;

/// Portfolio variance calculation using matrix multiplication
#[pyfunction]
fn portfolio_variance_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    cov_matrix: PyReadonlyArray2<f64>,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let cov_matrix = cov_matrix.as_array();
    
    // Calculate weights^T * cov_matrix * weights
    let temp = cov_matrix.dot(&weights);
    let variance = weights.dot(&temp);
    
    Ok(variance)
}

/// Downside standard deviation calculation for negative returns
#[pyfunction]
fn downside_std_rust(_py: Python, arr: PyReadonlyArray1<f64>) -> PyResult<f64> {
    let arr = arr.as_array();
    let mut sum = 0.0;
    let mut count = 0;
    
    for &x in arr.iter() {
        if x.is_finite() && x < 0.0 {
            sum += x * x;
            count += 1;
        }
    }
    
    if count > 0 {
        Ok((sum / count as f64).sqrt())
    } else {
        Ok(0.0)
    }
}

/// Negative Sharpe ratio calculation for optimization
#[pyfunction]
fn neg_sharpe_ratio_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    mean_returns: PyReadonlyArray1<f64>,
    cov_matrix: PyReadonlyArray2<f64>,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let mean_returns = mean_returns.as_array();
    let cov_matrix = cov_matrix.as_array();
    
    // Calculate portfolio return
    let mut port_return = 0.0;
    for (i, &w) in weights.iter().enumerate() {
        let w = if w.is_finite() { w } else { 0.0 };
        let mr = if mean_returns[i].is_finite() { mean_returns[i] } else { 0.0 };
        port_return += w * mr;
    }
    
    // Calculate portfolio variance
    let temp = cov_matrix.dot(&weights);
    let port_var = weights.dot(&temp);
    let port_var = if port_var.is_finite() { port_var } else { 0.0 };
    let port_vol = port_var.sqrt();
    let port_vol = if port_vol.is_finite() { port_vol } else { 0.0 };
    
    if port_vol > 0.0 {
        Ok(-port_return / port_vol)
    } else {
        Ok(0.0)
    }
}

/// Negative Sortino ratio calculation
#[pyfunction]
fn neg_sortino_ratio_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    mean_returns: PyReadonlyArray1<f64>,
    _cov_matrix: PyReadonlyArray2<f64>,
    returns_array: PyReadonlyArray1<f64>,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let mean_returns = mean_returns.as_array();
    let returns_array = returns_array.as_array();

    // Calculate portfolio return
    let mut port_return = 0.0;
    for (i, &w) in weights.iter().enumerate() {
        port_return += w * mean_returns[i];
    }

    // Clean the returns array: replace NaNs with 0
    let mut clean_returns = Vec::new();
    for &val in returns_array.iter() {
        if val.is_finite() {
            clean_returns.push(val);
        } else {
            clean_returns.push(0.0);
        }
    }

    // Compute downside standard deviation on negative returns
    let mut sum = 0.0;
    let mut count = 0;
    for &x in clean_returns.iter() {
        if x < 0.0 {
            sum += x * x;
            count += 1;
        }
    }

    let dd = if count > 0 {
        (sum / count as f64).sqrt()
    } else {
        0.0
    };

    if dd < MIN_FLOAT {
        Ok(f64::INFINITY)
    } else {
        Ok(-port_return / dd)
    }
}







/// VaR and CVaR calculation
#[pyfunction]
fn calculate_var_cvar_rust(
    _py: Python,
    returns_array: PyReadonlyArray1<f64>,
    confidence_level: f64,
) -> PyResult<(f64, f64)> {
    let returns_array = returns_array.as_array();

    // Clean the input array (remove NaNs)
    let mut valid_returns = Vec::new();
    for &x in returns_array.iter() {
        if x.is_finite() {
            valid_returns.push(x);
        }
    }

    // If no valid returns, return zeros
    if valid_returns.is_empty() {
        return Ok((0.0, 0.0));
    }

    // Sort returns from worst to best
    valid_returns.sort_by(|a, b| a.partial_cmp(b).unwrap());

    // Find the index at the specified confidence level
    let mut index = ((1.0 - confidence_level) * valid_returns.len() as f64) as usize;
    if index >= valid_returns.len() {
        index = valid_returns.len() - 1;
    }

    // VaR is the loss at this confidence level (negative of return)
    let var = -valid_returns[index];

    // CVaR is the average loss beyond VaR (including the VaR point)
    let tail_index = index + 1;
    let cvar = if tail_index > 0 && tail_index <= valid_returns.len() {
        let tail_sum: f64 = valid_returns[0..tail_index].iter().sum();
        -tail_sum / tail_index as f64
    } else {
        var // Fallback if index is 0 or out of bounds
    };

    Ok((var, cvar))
}



/// Negative modified Sharpe ratio calculation with skewness and kurtosis adjustment
#[pyfunction]
fn neg_modified_sharpe_ratio_rust(
    _py: Python,
    weights: PyReadonlyArray1<f64>,
    mean_returns: PyReadonlyArray1<f64>,
    cov_matrix: PyReadonlyArray2<f64>,
    returns_array: PyReadonlyArray2<f64>,
    risk_free_rate: f64,
) -> PyResult<f64> {
    let weights = weights.as_array();
    let mean_returns = mean_returns.as_array();
    let cov_matrix = cov_matrix.as_array();
    let returns_array = returns_array.as_array();

    let n = weights.len();

    // Compute portfolio expected return using mean returns
    let mut port_return = 0.0;
    for i in 0..n {
        port_return += weights[i] * mean_returns[i];
    }

    // Compute portfolio variance
    let temp = cov_matrix.dot(&weights);
    let port_var = weights.dot(&temp);
    let port_vol = port_var.sqrt();

    if port_vol <= MIN_FLOAT {
        return Ok(0.0);
    }

    // Compute portfolio return series from returns_array
    let t_len = returns_array.nrows();
    let mut port_series = vec![0.0; t_len];

    for t in 0..t_len {
        let mut s = 0.0;
        for j in 0..n {
            s += returns_array[[t, j]] * weights[j];
        }
        port_series[t] = s;
    }

    // Calculate sample mean and standard deviation of the series
    let series_mean: f64 = port_series.iter().sum::<f64>() / t_len as f64;

    let series_std = {
        let variance: f64 = port_series.iter()
            .map(|&x| (x - series_mean).powi(2))
            .sum::<f64>() / t_len as f64;
        variance.sqrt()
    };

    // Compute skewness and kurtosis
    let (skew, kurt) = if series_std > MIN_FLOAT {
        let skew: f64 = port_series.iter()
            .map(|&x| ((x - series_mean) / series_std).powi(3))
            .sum::<f64>() / t_len as f64;

        let kurt: f64 = port_series.iter()
            .map(|&x| ((x - series_mean) / series_std).powi(4))
            .sum::<f64>() / t_len as f64;

        (skew, kurt)
    } else {
        (0.0, 3.0) // Normal distribution assumption
    };

    // Use the 95% confidence level: standard normal quantile
    let z = 1.****************;

    // Cornish–Fisher expansion adjustment
    let z_mod = z + (1.0/6.0) * (z*z - 1.0) * skew +
                (1.0/24.0) * (z.powi(3) - 3.0*z) * (kurt - 3.0) -
                (1.0/36.0) * (2.0*z.powi(3) - 5.0*z) * (skew*skew);

    // Adjust volatility: scaling the normal volatility to account for skew/kurtosis
    let mod_vol = port_vol * (z_mod / z);

    // Modified Sharpe ratio (for minimization, return negative value)
    let sharpe_mod = (port_return - risk_free_rate) / mod_vol;
    Ok(-sharpe_mod)
}

/// Cross product calculation for convex hull algorithm
#[pyfunction]
fn cross_product_rust(
    _py: Python,
    o: (f64, f64),
    a: (f64, f64),
    b: (f64, f64)
) -> PyResult<f64> {
    Ok((a.0 - o.0) * (b.1 - o.1) - (a.1 - o.1) * (b.0 - o.0))
}

/// Efficient frontier calculation using Pareto optimality
#[pyfunction]
fn convex_hull_upper_rust(
    _py: Python,
    points: Vec<(f64, f64)>
) -> PyResult<Vec<(f64, f64)>> {
    if points.len() < 2 {
        return Ok(points);
    }

    // Remove duplicates and sort by risk (x-coordinate) ascending
    let mut sorted_points = points;
    sorted_points.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap_or(std::cmp::Ordering::Equal));
    sorted_points.dedup_by(|a, b| (a.0 - b.0).abs() < 1e-10 && (a.1 - b.1).abs() < 1e-10);

    // Build Pareto frontier using proper Pareto dominance
    let mut efficient = Vec::new();

    for (risk, return_val) in sorted_points {
        let mut is_dominated = false;

        // Check if this point is dominated by any point already in the efficient set
        for &(eff_risk, eff_return) in &efficient {
            // A point is dominated if another point has:
            // (lower or equal risk AND higher return) OR (lower risk AND equal or higher return)
            if (eff_risk <= risk && eff_return > return_val) || (eff_risk < risk && eff_return >= return_val) {
                is_dominated = true;
                break;
            }
        }

        if !is_dominated {
            // Remove any points in efficient set that are dominated by this new point
            efficient.retain(|&(eff_risk, eff_return)| {
                // Keep point if it's not dominated by the new point
                !((risk <= eff_risk && return_val > eff_return) || (risk < eff_risk && return_val >= eff_return))
            });

            efficient.push((risk, return_val));
        }
    }

    // Sort the final efficient frontier by risk
    efficient.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap_or(std::cmp::Ordering::Equal));

    Ok(efficient)
}

// ============================================================================
// PORTFOLIO OPTIMIZATION ENGINE
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationResult {
    pub weights: Vec<f64>,
    pub success: bool,
    pub objective_value: f64,
    pub iterations: usize,
    pub optimization_type: String,
}

#[derive(Debug, Clone)]
pub struct PortfolioData {
    pub mean_returns: Array1<f64>,
    pub cov_matrix: Array2<f64>,
    pub returns_matrix: Array2<f64>, // For historical calculations
    pub bounds: Vec<(f64, f64)>,
}

impl PortfolioData {
    pub fn new(
        mean_returns: Array1<f64>,
        cov_matrix: Array2<f64>,
        returns_matrix: Array2<f64>,
        bounds: Vec<(f64, f64)>,
    ) -> Self {
        Self {
            mean_returns,
            cov_matrix,
            returns_matrix,
            bounds,
        }
    }
}

// Objective function for minimum variance optimization
#[derive(Clone)]
pub struct MinVarianceObjective {
    pub cov_matrix: Array2<f64>,
}

impl CostFunction for MinVarianceObjective {
    type Param = Array1<f64>;
    type Output = f64;

    fn cost(&self, weights: &Self::Param) -> Result<Self::Output, argmin::core::Error> {
        let temp = self.cov_matrix.dot(weights);
        let variance = weights.dot(&temp);
        Ok(variance)
    }
}

impl Gradient for MinVarianceObjective {
    type Param = Array1<f64>;
    type Gradient = Array1<f64>;

    fn gradient(&self, weights: &Self::Param) -> Result<Self::Gradient, argmin::core::Error> {
        // Gradient of w^T * Σ * w = 2 * Σ * w
        let gradient = 2.0 * self.cov_matrix.dot(weights);
        Ok(gradient)
    }
}

// Objective function for maximum Sharpe ratio optimization
#[derive(Clone)]
pub struct MaxSharpeObjective {
    pub mean_returns: Array1<f64>,
    pub cov_matrix: Array2<f64>,
}

impl CostFunction for MaxSharpeObjective {
    type Param = Array1<f64>;
    type Output = f64;

    fn cost(&self, weights: &Self::Param) -> Result<Self::Output, argmin::core::Error> {
        let portfolio_return = self.mean_returns.dot(weights);
        let temp = self.cov_matrix.dot(weights);
        let portfolio_variance = weights.dot(&temp);

        if portfolio_variance <= MIN_FLOAT {
            return Ok(0.0);
        }

        let portfolio_volatility = portfolio_variance.sqrt();
        let sharpe_ratio = portfolio_return / portfolio_volatility;

        // Return negative for minimization
        Ok(-sharpe_ratio)
    }
}

impl Gradient for MaxSharpeObjective {
    type Param = Array1<f64>;
    type Gradient = Array1<f64>;

    fn gradient(&self, weights: &Self::Param) -> Result<Self::Gradient, argmin::core::Error> {
        let portfolio_return = self.mean_returns.dot(weights);
        let temp = self.cov_matrix.dot(weights);
        let portfolio_variance = weights.dot(&temp);

        if portfolio_variance <= MIN_FLOAT {
            return Ok(Array1::zeros(weights.len()));
        }

        let portfolio_volatility = portfolio_variance.sqrt();
        let cov_weights = 2.0 * self.cov_matrix.dot(weights);

        // Gradient of negative Sharpe ratio
        let grad = -(&self.mean_returns / portfolio_volatility -
                    (portfolio_return / (portfolio_volatility.powi(3))) * &cov_weights);

        Ok(grad)
    }
}

// Objective function for maximum Sortino ratio optimization
#[derive(Clone)]
pub struct MaxSortinoObjective {
    pub mean_returns: Array1<f64>,
    pub returns_matrix: Array2<f64>,
}

impl CostFunction for MaxSortinoObjective {
    type Param = Array1<f64>;
    type Output = f64;

    fn cost(&self, weights: &Self::Param) -> Result<Self::Output, argmin::core::Error> {
        let portfolio_return = self.mean_returns.dot(weights);

        // Calculate portfolio returns series
        // returns_matrix should be time_periods x assets, so we need to multiply correctly
        let portfolio_series = if self.returns_matrix.nrows() == weights.len() {
            // If returns_matrix is assets x time_periods, transpose it
            self.returns_matrix.t().dot(weights)
        } else {
            // If returns_matrix is time_periods x assets, use direct multiplication
            self.returns_matrix.dot(weights)
        };

        // Calculate downside deviation
        let mut downside_sum = 0.0;
        let mut count = 0;

        for &ret in portfolio_series.iter() {
            if ret.is_finite() && ret < 0.0 {
                downside_sum += ret * ret;
                count += 1;
            }
        }

        let downside_deviation = if count > 0 {
            (downside_sum / count as f64).sqrt()
        } else {
            MIN_FLOAT
        };

        if downside_deviation <= MIN_FLOAT {
            return Ok(0.0);
        }

        let sortino_ratio = portfolio_return / downside_deviation;

        // Return negative for minimization
        Ok(-sortino_ratio)
    }
}

impl Gradient for MaxSortinoObjective {
    type Param = Array1<f64>;
    type Gradient = Array1<f64>;

    fn gradient(&self, weights: &Self::Param) -> Result<Self::Gradient, argmin::core::Error> {
        // For Sortino ratio, we'll use numerical gradient since analytical is complex
        let epsilon = 1e-8;
        let mut gradient = Array1::zeros(weights.len());

        let base_cost = self.cost(weights)?;

        for i in 0..weights.len() {
            let mut weights_plus = weights.clone();
            weights_plus[i] += epsilon;

            let cost_plus = self.cost(&weights_plus)?;
            gradient[i] = (cost_plus - base_cost) / epsilon;
        }

        Ok(gradient)
    }
}

// Objective function for maximum Omega ratio optimization
#[derive(Clone)]
pub struct MaxOmegaObjective {
    pub returns_matrix: Array2<f64>,
    pub threshold: f64,
}

impl CostFunction for MaxOmegaObjective {
    type Param = Array1<f64>;
    type Output = f64;

    fn cost(&self, weights: &Self::Param) -> Result<Self::Output, argmin::core::Error> {
        // Calculate portfolio returns series
        let portfolio_series = if self.returns_matrix.nrows() == weights.len() {
            // If returns_matrix is assets x time_periods, transpose it
            self.returns_matrix.t().dot(weights)
        } else {
            // If returns_matrix is time_periods x assets
            self.returns_matrix.dot(weights)
        };

        // Calculate Omega ratio
        let mut gain_sum = 0.0;
        let mut loss_sum = 0.0;

        for &ret in portfolio_series.iter() {
            if ret.is_finite() {
                if ret > self.threshold {
                    gain_sum += ret - self.threshold;
                } else if ret < self.threshold {
                    loss_sum += self.threshold - ret;
                }
            }
        }

        if loss_sum < MIN_FLOAT {
            Ok(0.0) // Return 0 to minimize (we want to maximize Omega, so minimize negative Omega)
        } else {
            let omega_ratio = gain_sum / loss_sum;
            Ok(-omega_ratio) // Negative because we're minimizing
        }
    }
}

impl Gradient for MaxOmegaObjective {
    type Param = Array1<f64>;
    type Gradient = Array1<f64>;

    fn gradient(&self, weights: &Self::Param) -> Result<Self::Gradient, argmin::core::Error> {
        // Use numerical gradient for Omega ratio
        let epsilon = 1e-8;
        let mut gradient = Array1::zeros(weights.len());
        let base_cost = self.cost(weights)?;

        for i in 0..weights.len() {
            let mut weights_plus = weights.clone();
            weights_plus[i] += epsilon;
            let cost_plus = self.cost(&weights_plus)?;
            gradient[i] = (cost_plus - base_cost) / epsilon;
        }

        Ok(gradient)
    }
}

// Objective function for maximum Calmar ratio optimization
#[derive(Clone)]
pub struct MaxCalmarObjective {
    pub mean_returns: Array1<f64>,
    pub returns_matrix: Array2<f64>,
}

impl CostFunction for MaxCalmarObjective {
    type Param = Array1<f64>;
    type Output = f64;

    fn cost(&self, weights: &Self::Param) -> Result<Self::Output, argmin::core::Error> {
        let portfolio_return = self.mean_returns.dot(weights);

        // Calculate portfolio returns series
        let portfolio_series = if self.returns_matrix.nrows() == weights.len() {
            self.returns_matrix.t().dot(weights)
        } else {
            self.returns_matrix.dot(weights)
        };

        // Calculate maximum drawdown
        let mut cumulative = 0.0;
        let mut peak = 0.0;
        let mut max_drawdown = 0.0;

        for &ret in portfolio_series.iter() {
            if ret.is_finite() {
                cumulative += ret;
                if cumulative > peak {
                    peak = cumulative;
                }
                let drawdown = peak - cumulative;
                if drawdown > max_drawdown {
                    max_drawdown = drawdown;
                }
            }
        }

        if max_drawdown < MIN_FLOAT {
            // No drawdown means infinite Calmar ratio, so return large negative value for minimization
            Ok(-1e10)
        } else {
            let calmar_ratio = portfolio_return / max_drawdown;
            Ok(-calmar_ratio) // Negative because we're minimizing
        }
    }
}

impl Gradient for MaxCalmarObjective {
    type Param = Array1<f64>;
    type Gradient = Array1<f64>;

    fn gradient(&self, weights: &Self::Param) -> Result<Self::Gradient, argmin::core::Error> {
        // Use numerical gradient for Calmar ratio
        let epsilon = 1e-8;
        let mut gradient = Array1::zeros(weights.len());
        let base_cost = self.cost(weights)?;

        for i in 0..weights.len() {
            let mut weights_plus = weights.clone();
            weights_plus[i] += epsilon;
            let cost_plus = self.cost(&weights_plus)?;
            gradient[i] = (cost_plus - base_cost) / epsilon;
        }

        Ok(gradient)
    }
}

// Objective function for maximum Modified Sharpe ratio optimization
#[derive(Clone)]
pub struct MaxModifiedSharpeObjective {
    pub mean_returns: Array1<f64>,
    pub cov_matrix: Array2<f64>,
    pub returns_matrix: Array2<f64>,
    pub threshold: f64,
}

impl CostFunction for MaxModifiedSharpeObjective {
    type Param = Array1<f64>;
    type Output = f64;

    fn cost(&self, weights: &Self::Param) -> Result<Self::Output, argmin::core::Error> {
        let portfolio_return = self.mean_returns.dot(weights);

        // Calculate portfolio variance using covariance matrix (to match Python implementation)
        let temp = self.cov_matrix.dot(weights);
        let portfolio_variance = weights.dot(&temp);

        if portfolio_variance <= MIN_FLOAT {
            return Ok(0.0);
        }

        let portfolio_volatility = portfolio_variance.sqrt();

        // Calculate portfolio returns series for skewness/kurtosis calculation
        let portfolio_series = if self.returns_matrix.nrows() == weights.len() {
            self.returns_matrix.t().dot(weights)
        } else {
            self.returns_matrix.dot(weights)
        };

        if portfolio_volatility < MIN_FLOAT {
            return Ok(0.0);
        }

        // Calculate mean of portfolio series for standardization
        let mean_ret = portfolio_series.mean().unwrap_or(0.0);

        // Calculate skewness and kurtosis for Cornish-Fisher adjustment
        let mut skew_sum = 0.0;
        let mut kurt_sum = 0.0;
        let mut valid_count = 0;

        for &ret in portfolio_series.iter() {
            if ret.is_finite() {
                let standardized = (ret - mean_ret) / portfolio_volatility;
                skew_sum += standardized.powi(3);
                kurt_sum += standardized.powi(4);
                valid_count += 1;
            }
        }

        if valid_count < 3 {
            // Not enough data for skewness/kurtosis, fall back to regular Sharpe
            let sharpe = (portfolio_return - self.threshold) / portfolio_volatility;
            return Ok(-sharpe);
        }

        let skewness = skew_sum / valid_count as f64;
        let kurtosis = (kurt_sum / valid_count as f64) - 3.0; // Excess kurtosis

        // Standard normal quantile for 95% confidence level
        let z = 1.****************;

        // Cornish-Fisher expansion adjustment
        let z_mod = z + (1.0/6.0) * (z*z - 1.0) * skewness +
                   (1.0/24.0) * (z.powi(3) - 3.0*z) * kurtosis -
                   (1.0/36.0) * (2.0*z.powi(3) - 5.0*z) * (skewness*skewness);

        // Check for invalid z_mod values that could cause issues
        if !z_mod.is_finite() || z_mod.abs() < MIN_FLOAT {
            // Fall back to regular Sharpe if Cornish-Fisher produces invalid values
            let sharpe = (portfolio_return - self.threshold) / portfolio_volatility;
            return Ok(-sharpe);
        }

        // Adjust volatility: scaling the normal volatility to account for skew/kurtosis
        let mod_vol = portfolio_volatility * (z_mod / z);

        if mod_vol < MIN_FLOAT || !mod_vol.is_finite() {
            // Fall back to regular Sharpe if modified volatility is invalid
            let sharpe = (portfolio_return - self.threshold) / portfolio_volatility;
            Ok(-sharpe)
        } else {
            let modified_sharpe = (portfolio_return - self.threshold) / mod_vol;
            if modified_sharpe.is_finite() {
                Ok(-modified_sharpe) // Negative because we're minimizing
            } else {
                // Fall back to regular Sharpe if modified Sharpe is invalid
                let sharpe = (portfolio_return - self.threshold) / portfolio_volatility;
                Ok(-sharpe)
            }
        }
    }
}

impl Gradient for MaxModifiedSharpeObjective {
    type Param = Array1<f64>;
    type Gradient = Array1<f64>;

    fn gradient(&self, weights: &Self::Param) -> Result<Self::Gradient, argmin::core::Error> {
        // Use numerical gradient for Modified Sharpe ratio
        let epsilon = 1e-8;
        let mut gradient = Array1::zeros(weights.len());
        let base_cost = self.cost(weights)?;

        for i in 0..weights.len() {
            let mut weights_plus = weights.clone();
            weights_plus[i] += epsilon;
            let cost_plus = self.cost(&weights_plus)?;
            gradient[i] = (cost_plus - base_cost) / epsilon;
        }

        Ok(gradient)
    }
}

/// Constraint function to ensure weights sum to 1
fn weights_sum_constraint(weights: &Array1<f64>) -> f64 {
    weights.sum() - 1.0
}

/// Apply box constraints (bounds) to weights
fn apply_bounds(weights: &mut Array1<f64>, bounds: &[(f64, f64)]) {
    for (i, &mut ref mut w) in weights.iter_mut().enumerate() {
        if i < bounds.len() {
            let (min_bound, max_bound) = bounds[i];
            *w = w.max(min_bound).min(max_bound);
        }
    }
}

/// Normalize weights to sum to 1 while respecting bounds
fn normalize_weights_with_bounds(weights: &Array1<f64>, bounds: &[(f64, f64)]) -> Array1<f64> {
    let mut normalized = weights.clone();

    // Apply bounds first
    apply_bounds(&mut normalized, bounds);

    // Normalize to sum to 1
    let sum = normalized.sum();
    if sum.abs() > MIN_FLOAT {
        normalized /= sum;
    }

    // Apply bounds again after normalization
    apply_bounds(&mut normalized, bounds);

    normalized
}

/// Core optimization function using LBFGS
fn optimize_portfolio<T>(
    objective: T,
    initial_weights: Array1<f64>,
    bounds: &[(f64, f64)],
    max_iterations: usize,
) -> OptimizationResult
where
    T: CostFunction<Param = Array1<f64>, Output = f64> + Gradient<Param = Array1<f64>, Gradient = Array1<f64>> + Clone,
{
    // Simple gradient descent with projection for constraints
    let mut weights = initial_weights.clone();
    let mut best_weights = weights.clone();
    let mut best_cost = f64::INFINITY;
    let learning_rate = 0.01;
    let tolerance = 1e-8;

    for _iteration in 0..max_iterations {
        // Calculate cost and gradient
        let cost = match objective.cost(&weights) {
            Ok(c) => c,
            Err(_) => break,
        };

        let gradient = match objective.gradient(&weights) {
            Ok(g) => g,
            Err(_) => break,
        };

        // Update best solution
        if cost < best_cost {
            best_cost = cost;
            best_weights = weights.clone();
        }

        // Check convergence
        let grad_norm = gradient.iter().map(|x| x * x).sum::<f64>().sqrt();
        if grad_norm < tolerance {
            break;
        }

        // Gradient descent step
        weights = &weights - learning_rate * &gradient;

        // Project onto constraints
        weights = normalize_weights_with_bounds(&weights, bounds);

        // Adaptive learning rate (commented out for now)
        // if iteration > 10 && iteration % 10 == 0 {
        //     learning_rate *= 0.95;
        // }
    }

    OptimizationResult {
        weights: best_weights.to_vec(),
        success: best_cost.is_finite(),
        objective_value: best_cost,
        iterations: max_iterations,
        optimization_type: "LBFGS".to_string(),
    }
}

/// Rust-based portfolio optimization function
#[pyfunction]
fn optimize_portfolio_rust(
    _py: Python,
    mean_returns: PyReadonlyArray1<f64>,
    cov_matrix: PyReadonlyArray2<f64>,
    returns_matrix: PyReadonlyArray2<f64>,
    optimization_type: &str,
    bounds_lower: PyReadonlyArray1<f64>,
    bounds_upper: PyReadonlyArray1<f64>,
    max_iterations: Option<usize>,
) -> PyResult<(Vec<f64>, bool, f64)> {
    let mean_returns = mean_returns.as_array().to_owned();
    let cov_matrix = cov_matrix.as_array().to_owned();
    let returns_matrix = returns_matrix.as_array().to_owned();
    let bounds_lower = bounds_lower.as_array();
    let bounds_upper = bounds_upper.as_array();
    let max_iter = max_iterations.unwrap_or(200);

    // Create bounds vector
    let bounds: Vec<(f64, f64)> = bounds_lower.iter()
        .zip(bounds_upper.iter())
        .map(|(&lower, &upper)| (lower, upper))
        .collect();

    // Initial guess: equal weights
    let n_assets = mean_returns.len();
    let initial_weights = Array1::from_elem(n_assets, 1.0 / n_assets as f64);

    let result = match optimization_type {
        "min_variance" => {
            let objective = MinVarianceObjective {
                cov_matrix: cov_matrix.clone(),
            };
            optimize_portfolio(objective, initial_weights, &bounds, max_iter)
        },
        "max_sharpe" => {
            let objective = MaxSharpeObjective {
                mean_returns: mean_returns.clone(),
                cov_matrix: cov_matrix.clone(),
            };
            optimize_portfolio(objective, initial_weights, &bounds, max_iter)
        },
        "max_sortino" => {
            let objective = MaxSortinoObjective {
                mean_returns: mean_returns.clone(),
                returns_matrix: returns_matrix.clone(),
            };
            optimize_portfolio(objective, initial_weights, &bounds, max_iter)
        },
        "max_omega" => {
            let objective = MaxOmegaObjective {
                returns_matrix: returns_matrix.clone(),
                threshold: 0.0005, // 0.1% daily
            };
            optimize_portfolio(objective, initial_weights, &bounds, max_iter)
        },
        "max_calmar" => {
            let objective = MaxCalmarObjective {
                mean_returns: mean_returns.clone(),
                returns_matrix: returns_matrix.clone(),
            };
            optimize_portfolio(objective, initial_weights, &bounds, max_iter)
        },
        "max_modified_sharpe" => {
            let objective = MaxModifiedSharpeObjective {
                mean_returns: mean_returns.clone(),
                cov_matrix: cov_matrix.clone(),
                returns_matrix: returns_matrix.clone(),
                threshold: 0.0,
            };
            optimize_portfolio(objective, initial_weights, &bounds, max_iter)
        },
        _ => {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Unknown optimization type: {}", optimization_type)
            ));
        }
    };

    Ok((result.weights, result.success, result.objective_value))
}

/// Batch portfolio optimization for multiple combinations
#[pyfunction]
fn batch_optimize_portfolios_rust(
    _py: Python,
    combinations_data: Vec<(Vec<f64>, Vec<Vec<f64>>, Vec<Vec<f64>>)>, // (mean_returns, cov_matrix, returns_matrix) for each combo
    optimization_types: Vec<String>,
    bounds_lower: PyReadonlyArray1<f64>,
    bounds_upper: PyReadonlyArray1<f64>,
    max_iterations: Option<usize>,
) -> PyResult<Vec<Vec<(Vec<f64>, bool, f64, String)>>> {
    let bounds_lower = bounds_lower.as_array();
    let bounds_upper = bounds_upper.as_array();
    let max_iter = max_iterations.unwrap_or(200);

    // Create bounds vector
    let bounds: Vec<(f64, f64)> = bounds_lower.iter()
        .zip(bounds_upper.iter())
        .map(|(&lower, &upper)| (lower, upper))
        .collect();

    // Process combinations in parallel
    let results: Vec<Vec<(Vec<f64>, bool, f64, String)>> = combinations_data
        .par_iter()
        .map(|(mean_returns_vec, cov_matrix_vec, returns_matrix_vec)| {
            let mean_returns = Array1::from_vec(mean_returns_vec.clone());
            let cov_matrix = Array2::from_shape_vec(
                (mean_returns_vec.len(), mean_returns_vec.len()),
                cov_matrix_vec.iter().flatten().cloned().collect()
            ).unwrap_or_else(|_| Array2::zeros((mean_returns_vec.len(), mean_returns_vec.len())));

            let returns_matrix = Array2::from_shape_vec(
                (returns_matrix_vec.len(), mean_returns_vec.len()),
                returns_matrix_vec.iter().flatten().cloned().collect()
            ).unwrap_or_else(|_| Array2::zeros((returns_matrix_vec.len(), mean_returns_vec.len())));

            let n_assets = mean_returns.len();
            let initial_weights = Array1::from_elem(n_assets, 1.0 / n_assets as f64);

            let mut combo_results = Vec::new();

            for opt_type in &optimization_types {
                let result = match opt_type.as_str() {
                    "min_variance" => {
                        let objective = MinVarianceObjective {
                            cov_matrix: cov_matrix.clone(),
                        };
                        optimize_portfolio(objective, initial_weights.clone(), &bounds, max_iter)
                    },
                    "max_sharpe" => {
                        let objective = MaxSharpeObjective {
                            mean_returns: mean_returns.clone(),
                            cov_matrix: cov_matrix.clone(),
                        };
                        optimize_portfolio(objective, initial_weights.clone(), &bounds, max_iter)
                    },
                    "max_sortino" => {
                        let objective = MaxSortinoObjective {
                            mean_returns: mean_returns.clone(),
                            returns_matrix: returns_matrix.clone(),
                        };
                        optimize_portfolio(objective, initial_weights.clone(), &bounds, max_iter)
                    },
                    "max_omega" => {
                        let objective = MaxOmegaObjective {
                            returns_matrix: returns_matrix.clone(),
                            threshold: 0.001, // 0.1% daily
                        };
                        optimize_portfolio(objective, initial_weights.clone(), &bounds, max_iter)
                    },
                    "max_calmar" => {
                        let objective = MaxCalmarObjective {
                            mean_returns: mean_returns.clone(),
                            returns_matrix: returns_matrix.clone(),
                        };
                        optimize_portfolio(objective, initial_weights.clone(), &bounds, max_iter)
                    },
                    "max_modified_sharpe" => {
                        let objective = MaxModifiedSharpeObjective {
                            mean_returns: mean_returns.clone(),
                            cov_matrix: cov_matrix.clone(),
                            returns_matrix: returns_matrix.clone(),
                            threshold: 0.0,
                        };
                        optimize_portfolio(objective, initial_weights.clone(), &bounds, max_iter)
                    },
                    _ => continue,
                };

                combo_results.push((
                    result.weights,
                    result.success,
                    result.objective_value,
                    opt_type.clone(),
                ));
            }

            combo_results
        })
        .collect();

    Ok(results)
}

/// Python module definition
#[pymodule]
fn ratio_calcs_rust(m: &Bound<'_, PyModule>) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(portfolio_variance_rust, m)?)?;
    m.add_function(wrap_pyfunction!(downside_std_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_sharpe_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_sortino_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(compute_omega_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_calmar_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(compute_calmar_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(calculate_var_cvar_rust, m)?)?;
    m.add_function(wrap_pyfunction!(compute_martin_ratio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(neg_modified_sharpe_ratio_rust, m)?)?;

    // Efficient frontier functions
    m.add_function(wrap_pyfunction!(convex_hull_upper_rust, m)?)?;
    m.add_function(wrap_pyfunction!(cross_product_rust, m)?)?;

    // Portfolio optimization functions
    m.add_function(wrap_pyfunction!(optimize_portfolio_rust, m)?)?;
    m.add_function(wrap_pyfunction!(batch_optimize_portfolios_rust, m)?)?;

    Ok(())
}
